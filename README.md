# EMDrafter - AI-Powered Email Reply Assistant

A Windows-compatible desktop application that helps you draft intelligent email replies using AI.

## Features

- **Secure Gmail Authentication**: Client-style login with IMAP/SMTP over SSL/TLS
- **Conversation Retrieval**: Fetch complete email threads with specific contacts
- **AI-Powered Drafting**: Generate contextual replies using Deepseek API
- **User-Friendly GUI**: Clean tkinter interface optimized for Windows
- **Security First**: Secure password handling and API key management
- **Production Ready**: Comprehensive error handling and logging

## Requirements

- Windows 10 or later
- Python 3.8 or later
- Gmail account with "Less secure app access" enabled OR App Password configured
- Deepseek API key

## Installation

1. Clone or download this repository
2. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Copy `.env.template` to `.env` and configure your settings:
   ```bash
   copy .env.template .env
   ```
4. Edit `.env` file and add your Deepseek API key

## Configuration

### Gmail Setup
For Gmail authentication, you have two options:

1. **App Password (Recommended)**:
   - Enable 2-factor authentication on your Google account
   - Generate an App Password for this application
   - Use your Gmail address and the App Password in the application

2. **Less Secure Apps** (Not recommended):
   - Enable "Less secure app access" in your Google account settings
   - Use your regular Gmail credentials

### Deepseek API
1. Sign up at https://platform.deepseek.com/
2. Generate an API key
3. Add the key to your `.env` file

## Usage

1. Run the application:
   ```bash
   python main.py
   ```
2. Enter your Gmail credentials
3. Specify the email address you want to find conversations with
4. Click "Fetch Conversation" to retrieve the latest thread
5. Review the conversation history
6. Click "Draft AI Reply" to generate a response
7. Edit the draft if needed
8. Send the reply or save as draft

## Security Notes

- Passwords are never stored or logged
- API keys are loaded from environment variables
- All email communication uses SSL/TLS encryption
- Input validation prevents injection attacks

## Troubleshooting

- **Login Issues**: Ensure Gmail App Password is correctly configured
- **API Errors**: Verify your Deepseek API key is valid and has sufficient credits
- **Connection Problems**: Check your internet connection and firewall settings

## License

This project is for educational and personal use.
