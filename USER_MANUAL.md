# EMDrafter User Manual

## Table of Contents
1. [Installation](#installation)
2. [Initial Setup](#initial-setup)
3. [Gmail Configuration](#gmail-configuration)
4. [Using the Application](#using-the-application)
5. [Troubleshooting](#troubleshooting)
6. [Security Notes](#security-notes)
7. [FAQ](#faq)

## Installation

### System Requirements
- Windows 10 or later
- Python 3.8 or later
- Internet connection
- Gmail account
- Deepseek API account

### Step 1: Install Python
1. Download Python from https://python.org/downloads/
2. During installation, check "Add Python to PATH"
3. Verify installation by opening Command Prompt and typing: `python --version`

### Step 2: Download EMDrafter
1. Download or clone the EMDrafter application files
2. Extract to a folder (e.g., `C:\EMDrafter`)

### Step 3: Install Dependencies
1. Open Command Prompt as Administrator
2. Navigate to the EMDrafter folder: `cd C:\EMDrafter`
3. Install required packages: `pip install -r requirements.txt`

### Step 4: Configure Environment
1. Copy `.env.template` to `.env`
2. Edit `.env` file with your settings (see Initial Setup section)

## Initial Setup

### Configure API Key
1. Sign up at https://platform.deepseek.com/
2. Generate an API key
3. Open `.env` file in a text editor
4. Replace `your_deepseek_api_key_here` with your actual API key
5. Save the file

### Example .env Configuration
```
DEEPSEEK_API_KEY=sk-your-actual-api-key-here
DEEPSEEK_API_BASE=https://api.deepseek.com
APP_NAME=EMDrafter
LOG_LEVEL=INFO
MAX_CONVERSATION_LENGTH=10000
EMAIL_TIMEOUT=30
API_TIMEOUT=60
```

## Gmail Configuration

### Option 1: App Password (Recommended)
1. Enable 2-factor authentication on your Google account
2. Go to Google Account settings → Security → App passwords
3. Generate an app password for "Mail"
4. Use this app password in EMDrafter (not your regular password)

### Option 2: Less Secure Apps (Not Recommended)
1. Go to Google Account settings → Security
2. Enable "Less secure app access"
3. Use your regular Gmail password in EMDrafter

**Note:** Google is phasing out less secure app access. App passwords are more secure and recommended.

## Using the Application

### Starting EMDrafter
1. Open Command Prompt
2. Navigate to EMDrafter folder: `cd C:\EMDrafter`
3. Run the application: `python main.py`

### Main Interface

#### 1. Gmail Authentication
- **Gmail Address**: Enter your full Gmail address (e.g., <EMAIL>)
- **Password/App Password**: Enter your app password or regular password
- **Test Connection**: Click to verify your credentials

#### 2. Email Conversation
- **Email Address to Search**: Enter the email address you want to find conversations with
- **Fetch Conversation**: Click to retrieve the latest email thread
- **Conversation History**: View the complete conversation thread

#### 3. AI Reply Generation
- **Draft AI Reply**: Click to generate an AI-powered reply based on the conversation
- **Generated Reply**: Edit the AI-generated reply if needed
- **Send Reply**: Send the reply immediately
- **Save Draft**: Save the reply for later sending

### Workflow Example
1. Enter your Gmail credentials and test connection
2. Enter the email address you want to reply to (e.g., <EMAIL>)
3. Click "Fetch Conversation" to load the email thread
4. Review the conversation history
5. Click "Draft AI Reply" to generate a response
6. Edit the generated reply if needed
7. Either "Send Reply" immediately or "Save Draft" for later

## Troubleshooting

### Common Issues

#### SMTP Timeout Error
**Problem**: "SMTP object has no attribute 'settimeout'" error
**Solution**:
- Run `fix_smtp_error.bat` to automatically fix this issue
- Or manually run: `pip uninstall secure-smtplib -y` then `pip install -r requirements.txt`

#### Authentication Failed
**Problem**: "Authentication failed" error when testing connection
**Solutions**:
- Verify your email address is correct
- If using App Password, ensure it's correctly copied (no spaces)
- Check that 2-factor authentication is enabled for App Password
- Try enabling "Less secure app access" temporarily

#### No Conversations Found
**Problem**: "No conversation found with this email address"
**Solutions**:
- Verify the target email address is correct
- Check that you have actually exchanged emails with this address
- Try searching for a different email address
- Check your Gmail inbox to confirm emails exist

#### API Key Error
**Problem**: "API key error" or "Invalid API key"
**Solutions**:
- Verify your Deepseek API key is correct in the .env file
- Check that you have sufficient API credits
- Ensure there are no extra spaces in the API key
- Try generating a new API key

#### Connection Timeout
**Problem**: Operations timeout or fail to connect
**Solutions**:
- Check your internet connection
- Try increasing timeout values in .env file
- Check if your firewall is blocking the application
- Restart the application

#### Application Won't Start
**Problem**: Application fails to start or crashes immediately
**Solutions**:
- Check that all dependencies are installed: `pip install -r requirements.txt`
- Verify Python version is 3.8 or later: `python --version`
- Check the log file (emdrafter.log) for error details
- Try running: `python test_emdrafter.py` to check for issues

### Log Files
- Application logs are saved to `emdrafter.log`
- Check this file for detailed error information
- Logs include timestamps and error details for troubleshooting

## Security Notes

### Password Security
- Passwords are never stored permanently
- Use App Passwords instead of regular passwords when possible
- Passwords are only kept in memory during the session

### API Key Security
- Store API keys in the .env file only
- Never share your .env file
- Regenerate API keys if compromised

### Email Security
- All email communication uses SSL/TLS encryption
- Draft files are stored locally in plain text
- Clear drafts folder periodically for security

### Network Security
- Application only connects to Gmail and Deepseek servers
- No data is sent to other third parties
- All connections use secure protocols

## FAQ

### Q: Can I use this with other email providers?
A: Currently, EMDrafter is designed specifically for Gmail. Support for other providers may be added in future versions.

### Q: How much does it cost to use?
A: EMDrafter is free, but you need a Deepseek API account which has usage-based pricing. Check Deepseek's pricing page for current rates.

### Q: Can I customize the AI prompts?
A: Currently, the AI prompt is built-in, but future versions may allow customization.

### Q: Is my email data stored anywhere?
A: No, email data is only processed temporarily. Drafts are saved locally if you choose to save them.

### Q: Can I use this on Mac or Linux?
A: The application is designed for Windows but may work on other platforms with minor modifications.

### Q: How do I update the application?
A: Download the latest version and replace the files, keeping your .env configuration.

### Q: What if I forget my App Password?
A: You can generate a new App Password from your Google Account settings.

### Q: Can I reply to multiple people at once?
A: Currently, the application handles one conversation at a time.

## Support

For additional support:
1. Check the log file (emdrafter.log) for error details
2. Run the test suite: `python test_emdrafter.py`
3. Review this manual and the README.md file
4. Check that all requirements are properly installed

## Version Information
- Current Version: 1.0.0
- Last Updated: 2024
- Compatible with: Windows 10+, Python 3.8+
