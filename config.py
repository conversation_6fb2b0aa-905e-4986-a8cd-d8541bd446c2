"""
Configuration management for EMDrafter application.
Handles loading environment variables and application settings.
"""

import os
import logging
from dotenv import load_dotenv
from typing import Optional

class Config:
    """Configuration class for EMDrafter application."""
    
    def __init__(self):
        """Initialize configuration by loading environment variables."""
        # Load environment variables from .env file
        load_dotenv()
        
        # Deepseek API Configuration
        self.deepseek_api_key: Optional[str] = os.getenv('DEEPSEEK_API_KEY')
        self.deepseek_api_base: str = os.getenv('DEEPSEEK_API_BASE', 'https://api.deepseek.com')
        
        # Application Configuration
        self.app_name: str = os.getenv('APP_NAME', 'EMDrafter')
        self.app_version: str = os.getenv('APP_VERSION', '1.0.0')
        
        # Logging Configuration
        self.log_level: str = os.getenv('LOG_LEVEL', 'INFO')
        self.log_file: str = os.getenv('LOG_FILE', 'emdrafter.log')
        
        # Email Configuration
        self.default_imap_server: str = os.getenv('DEFAULT_IMAP_SERVER', 'imap.gmail.com')
        self.default_imap_port: int = int(os.getenv('DEFAULT_IMAP_PORT', '993'))
        self.default_smtp_server: str = os.getenv('DEFAULT_SMTP_SERVER', 'smtp.gmail.com')
        self.default_smtp_port: int = int(os.getenv('DEFAULT_SMTP_PORT', '587'))
        
        # Security Configuration
        self.max_conversation_length: int = int(os.getenv('MAX_CONVERSATION_LENGTH', '10000'))
        
        # Timeout settings
        self.email_timeout: int = int(os.getenv('EMAIL_TIMEOUT', '30'))
        self.api_timeout: int = int(os.getenv('API_TIMEOUT', '60'))
        
        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        log_level = getattr(logging, self.log_level.upper(), logging.INFO)
        
        # Create logs directory if it doesn't exist
        log_dir = os.path.dirname(self.log_file) if os.path.dirname(self.log_file) else '.'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Configure logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
    
    def validate_config(self) -> tuple[bool, list[str]]:
        """
        Validate configuration settings.
        
        Returns:
            tuple: (is_valid, list_of_errors)
        """
        errors = []
        
        # Check required API key
        if not self.deepseek_api_key:
            errors.append("DEEPSEEK_API_KEY is required but not set")
        
        # Validate timeout values
        if self.email_timeout <= 0:
            errors.append("EMAIL_TIMEOUT must be positive")
        
        if self.api_timeout <= 0:
            errors.append("API_TIMEOUT must be positive")
        
        # Validate conversation length
        if self.max_conversation_length <= 0:
            errors.append("MAX_CONVERSATION_LENGTH must be positive")
        
        return len(errors) == 0, errors
    
    def get_window_title(self) -> str:
        """Get formatted window title."""
        return f"{self.app_name} v{self.app_version}"

# Global configuration instance
config = Config()
