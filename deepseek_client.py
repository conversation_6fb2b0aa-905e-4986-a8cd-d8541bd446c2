"""
Deepseek API client for EMDrafter.
Handles AI-powered email reply generation with conversation context processing.
"""

import requests
import json
import logging
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from config import config

logger = logging.getLogger(__name__)

class DeepseekClient:
    """Client for interacting with Deepseek API."""
    
    def __init__(self):
        """Initialize the Deepseek client."""
        self.api_key = config.deepseek_api_key
        self.api_base = config.deepseek_api_base
        self.timeout = config.api_timeout
        self.max_conversation_length = config.max_conversation_length
    
    def validate_api_key(self) -> Tuple[bool, str]:
        """
        Validate the API key by making a test request.
        
        Returns:
            tuple: (is_valid, error_message)
        """
        if not self.api_key:
            return False, "API key not configured"
        
        try:
            # Make a simple test request
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            test_payload = {
                "model": "deepseek-chat",
                "messages": [
                    {"role": "user", "content": "Hello"}
                ],
                "max_tokens": 10
            }
            
            response = requests.post(
                f"{self.api_base}/v1/chat/completions",
                headers=headers,
                json=test_payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return True, "API key is valid"
            elif response.status_code == 401:
                return False, "Invalid API key"
            else:
                return False, f"API error: {response.status_code}"
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API validation error: {str(e)}")
            return False, f"Connection error: {str(e)}"
    
    def generate_reply(self, conversation_text: str, 
                      custom_prompt: Optional[str] = None) -> Tuple[bool, str, str]:
        """
        Generate an AI reply based on conversation context.
        
        Args:
            conversation_text: The formatted conversation history
            custom_prompt: Optional custom prompt for reply generation
            
        Returns:
            tuple: (success, generated_reply, error_message)
        """
        try:
            if not self.api_key:
                return False, "", "API key not configured"
            
            # Prepare the conversation for AI processing
            processed_conversation = self._process_conversation(conversation_text)
            
            # Create the prompt
            prompt = self._create_prompt(processed_conversation, custom_prompt)
            
            # Make API request
            success, response_data, error = self._make_api_request(prompt)
            
            if not success:
                return False, "", error
            
            # Extract the generated reply
            reply = self._extract_reply(response_data)
            
            if not reply:
                return False, "", "Failed to extract reply from API response"
            
            logger.info("Successfully generated AI reply")
            return True, reply, ""
            
        except Exception as e:
            logger.error(f"Error generating reply: {str(e)}")
            return False, "", f"Error generating reply: {str(e)}"
    
    def _process_conversation(self, conversation_text: str) -> str:
        """
        Process conversation text to fit within token limits.
        
        Args:
            conversation_text: Raw conversation text
            
        Returns:
            Processed conversation text
        """
        # If conversation is too long, truncate from the beginning
        # but keep the most recent messages
        if len(conversation_text) > self.max_conversation_length:
            logger.warning(f"Conversation too long ({len(conversation_text)} chars), truncating")
            
            # Find a good truncation point (try to keep complete messages)
            truncated = conversation_text[-self.max_conversation_length:]
            
            # Try to find the start of a complete message
            message_start = truncated.find("Message ")
            if message_start > 0:
                truncated = truncated[message_start:]
            
            truncated = "[...conversation truncated...]\n\n" + truncated
            return truncated
        
        return conversation_text
    
    def _create_prompt(self, conversation_text: str, custom_prompt: Optional[str] = None) -> str:
        """
        Create the prompt for AI reply generation.
        
        Args:
            conversation_text: Processed conversation text
            custom_prompt: Optional custom prompt
            
        Returns:
            Complete prompt for AI
        """
        if custom_prompt:
            base_prompt = custom_prompt
        else:
            base_prompt = """Based on the following email conversation, write a polite, professional, and context-aware reply. 
The reply should:
1. Address the main points from the most recent message
2. Maintain a professional but friendly tone
3. Be concise and to the point
4. Include appropriate greetings and closings
5. Be helpful and constructive

Please write only the email reply content, without any additional commentary or explanations."""
        
        full_prompt = f"{base_prompt}\n\nEmail Conversation:\n{conversation_text}\n\nYour Reply:"
        
        return full_prompt
    
    def _make_api_request(self, prompt: str) -> Tuple[bool, Optional[Dict[Any, Any]], str]:
        """
        Make the actual API request to Deepseek.
        
        Args:
            prompt: The complete prompt for AI
            
        Returns:
            tuple: (success, response_data, error_message)
        """
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a helpful email assistant that writes professional and contextually appropriate email replies."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.7,
                "top_p": 0.9
            }
            
            logger.info("Making API request to Deepseek")
            response = requests.post(
                f"{self.api_base}/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return True, response.json(), ""
            else:
                error_msg = f"API request failed with status {response.status_code}"
                try:
                    error_data = response.json()
                    if 'error' in error_data:
                        error_msg += f": {error_data['error'].get('message', 'Unknown error')}"
                except:
                    error_msg += f": {response.text}"
                
                logger.error(error_msg)
                return False, None, error_msg
                
        except requests.exceptions.Timeout:
            error_msg = "API request timed out"
            logger.error(error_msg)
            return False, None, error_msg
        except requests.exceptions.RequestException as e:
            error_msg = f"API request failed: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg
    
    def _extract_reply(self, response_data: Dict[Any, Any]) -> Optional[str]:
        """
        Extract the generated reply from API response.
        
        Args:
            response_data: API response data
            
        Returns:
            Generated reply text or None if extraction failed
        """
        try:
            if 'choices' not in response_data or not response_data['choices']:
                logger.error("No choices in API response")
                return None
            
            choice = response_data['choices'][0]
            
            if 'message' not in choice or 'content' not in choice['message']:
                logger.error("No message content in API response")
                return None
            
            reply = choice['message']['content'].strip()
            
            # Clean up the reply
            reply = self._clean_reply(reply)
            
            return reply
            
        except Exception as e:
            logger.error(f"Error extracting reply: {str(e)}")
            return None
    
    def _clean_reply(self, reply: str) -> str:
        """
        Clean up the generated reply.
        
        Args:
            reply: Raw generated reply
            
        Returns:
            Cleaned reply
        """
        # Remove any leading/trailing whitespace
        reply = reply.strip()
        
        # Remove common AI response prefixes
        prefixes_to_remove = [
            "Here's a reply:",
            "Here is a reply:",
            "Reply:",
            "Email reply:",
            "Response:",
        ]
        
        for prefix in prefixes_to_remove:
            if reply.lower().startswith(prefix.lower()):
                reply = reply[len(prefix):].strip()
        
        return reply

# Global Deepseek client instance
deepseek_client = DeepseekClient()
