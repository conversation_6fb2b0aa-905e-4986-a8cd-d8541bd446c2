"""
Email operations module for EMDrafter.
Handles email sending functionality via SMTP with draft saving capabilities.
"""

import smtplib
import logging
import os
import json
from typing import <PERSON>ple, Optional
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from email.header import <PERSON><PERSON>
from gmail_auth import gmail_auth
from config import config

logger = logging.getLogger(__name__)

class EmailOperations:
    """Handles email sending and draft management operations."""
    
    def __init__(self):
        """Initialize email operations."""
        self.drafts_folder = "drafts"
        self._ensure_drafts_folder()
    
    def _ensure_drafts_folder(self) -> None:
        """Ensure the drafts folder exists."""
        if not os.path.exists(self.drafts_folder):
            os.makedirs(self.drafts_folder)
            logger.info(f"Created drafts folder: {self.drafts_folder}")
    
    def send_email(self, user_email: str, password: str, to_email: str, 
                   subject: str, body: str, reply_to_message_id: Optional[str] = None) -> Tuple[bool, str]:
        """
        Send an email via SMTP.
        
        Args:
            user_email: Sender's email address
            password: Sender's password or App Password
            to_email: Recipient's email address
            subject: Email subject
            body: Email body text
            reply_to_message_id: Optional message ID to reply to
            
        Returns:
            tuple: (success, error_message)
        """
        try:
            # Get SMTP connection
            smtp = gmail_auth.get_smtp_connection(user_email, password)
            if not smtp:
                return False, "Failed to connect to Gmail SMTP server"
            
            # Create message
            message = self._create_message(user_email, to_email, subject, body, reply_to_message_id)
            
            # Send email
            smtp.send_message(message)
            smtp.quit()
            
            logger.info(f"Email sent successfully to {to_email}")
            return True, "Email sent successfully"
            
        except smtplib.SMTPException as e:
            logger.error(f"SMTP error sending email: {str(e)}")
            return False, f"Failed to send email: {str(e)}"
        except Exception as e:
            logger.error(f"Error sending email: {str(e)}")
            return False, f"Error sending email: {str(e)}"
    
    def _create_message(self, from_email: str, to_email: str, subject: str, 
                       body: str, reply_to_message_id: Optional[str] = None) -> MIMEMultipart:
        """
        Create an email message.
        
        Args:
            from_email: Sender's email address
            to_email: Recipient's email address
            subject: Email subject
            body: Email body text
            reply_to_message_id: Optional message ID to reply to
            
        Returns:
            MIMEMultipart message object
        """
        # Create message
        message = MIMEMultipart()
        
        # Set headers
        message['From'] = from_email
        message['To'] = to_email
        message['Subject'] = Header(subject, 'utf-8')
        message['Date'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')
        
        # Add reply-to headers if this is a reply
        if reply_to_message_id:
            message['In-Reply-To'] = reply_to_message_id
            message['References'] = reply_to_message_id
        
        # Add body
        body_part = MIMEText(body, 'plain', 'utf-8')
        message.attach(body_part)
        
        return message
    
    def save_draft(self, from_email: str, to_email: str, subject: str,
                   body: str, reply_to_message_id: Optional[str] = None,
                   password: Optional[str] = None) -> Tuple[bool, str, str]:
        """
        Save an email as a draft both locally and in Gmail Drafts folder.

        Args:
            from_email: Sender's email address
            to_email: Recipient's email address
            subject: Email subject
            body: Email body text
            reply_to_message_id: Optional message ID to reply to
            password: Gmail password for saving to Gmail Drafts

        Returns:
            tuple: (success, draft_filename, error_message)
        """
        try:
            # Create draft data for local storage
            draft_data = {
                'from': from_email,
                'to': to_email,
                'subject': subject,
                'body': body,
                'reply_to_message_id': reply_to_message_id,
                'created_at': datetime.now().isoformat(),
                'version': '1.0'
            }

            # Generate filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            safe_subject = self._make_safe_filename(subject)
            filename = f"draft_{timestamp}_{safe_subject}.json"
            filepath = os.path.join(self.drafts_folder, filename)

            # Save draft locally
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(draft_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Draft saved locally: {filename}")

            # Save draft to Gmail Drafts folder if password provided
            gmail_success = True
            gmail_error = ""

            if password:
                gmail_success, gmail_error = self._save_draft_to_gmail(
                    from_email, to_email, subject, body, reply_to_message_id, password
                )

                if gmail_success:
                    logger.info("Draft saved to Gmail Drafts folder")
                    draft_data['saved_to_gmail'] = True
                else:
                    logger.warning(f"Failed to save draft to Gmail: {gmail_error}")
                    draft_data['saved_to_gmail'] = False
                    draft_data['gmail_error'] = gmail_error

                # Update local file with Gmail save status
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(draft_data, f, indent=2, ensure_ascii=False)

            # Return success if local save worked (Gmail save is optional)
            status_msg = "Draft saved locally"
            if password:
                if gmail_success:
                    status_msg += " and to Gmail Drafts"
                else:
                    status_msg += f" (Gmail save failed: {gmail_error})"

            return True, filename, status_msg
            
        except Exception as e:
            logger.error(f"Error saving draft: {str(e)}")
            return False, "", f"Error saving draft: {str(e)}"

    def _save_draft_to_gmail(self, from_email: str, to_email: str, subject: str,
                            body: str, reply_to_message_id: Optional[str],
                            password: str) -> Tuple[bool, str]:
        """
        Save draft to Gmail Drafts folder using IMAP.

        Args:
            from_email: Sender's email address
            to_email: Recipient's email address
            subject: Email subject
            body: Email body text
            reply_to_message_id: Optional message ID to reply to
            password: Gmail password

        Returns:
            tuple: (success, error_message)
        """
        try:
            # Create the email message
            message = self._create_message(from_email, to_email, subject, body, reply_to_message_id)

            # Convert message to string format for IMAP
            message_str = message.as_string()

            # Connect to Gmail IMAP
            import imaplib
            import ssl

            # Create SSL context
            context = ssl.create_default_context()

            # Connect to Gmail IMAP server
            imap = imaplib.IMAP4_SSL(
                config.default_imap_server,
                config.default_imap_port,
                ssl_context=context
            )

            # Set timeout
            imap.sock.settimeout(config.email_timeout)

            # Authenticate
            imap.login(from_email, password)

            # Select Drafts folder (try different possible names)
            drafts_folders = ['[Gmail]/Drafts', 'Drafts', '[Google Mail]/Drafts', 'INBOX.Drafts']
            drafts_folder = None

            for folder in drafts_folders:
                try:
                    result = imap.select(folder)
                    if result[0] == 'OK':
                        drafts_folder = folder
                        break
                except:
                    continue

            if not drafts_folder:
                # List available folders to help with debugging
                folder_list = imap.list()
                logger.warning(f"Could not find Drafts folder. Available folders: {folder_list}")
                imap.logout()
                return False, "Could not find Gmail Drafts folder"

            # Append the draft message to Drafts folder
            # Use IMAP APPEND command to add message to Drafts
            result = imap.append(drafts_folder, '\\Draft', None, message_str.encode('utf-8'))

            if result[0] == 'OK':
                logger.info(f"Draft successfully saved to Gmail folder: {drafts_folder}")
                success = True
                error_msg = ""
            else:
                logger.error(f"Failed to save draft to Gmail: {result}")
                success = False
                error_msg = f"IMAP append failed: {result[1] if len(result) > 1 else 'Unknown error'}"

            # Close connection
            imap.logout()

            return success, error_msg

        except Exception as e:
            logger.error(f"Error saving draft to Gmail: {str(e)}")
            return False, f"Gmail draft save error: {str(e)}"
    
    def load_draft(self, filename: str) -> Tuple[bool, dict, str]:
        """
        Load a draft from file.
        
        Args:
            filename: Draft filename
            
        Returns:
            tuple: (success, draft_data, error_message)
        """
        try:
            filepath = os.path.join(self.drafts_folder, filename)
            
            if not os.path.exists(filepath):
                return False, {}, f"Draft file not found: {filename}"
            
            with open(filepath, 'r', encoding='utf-8') as f:
                draft_data = json.load(f)
            
            logger.info(f"Draft loaded: {filename}")
            return True, draft_data, ""
            
        except Exception as e:
            logger.error(f"Error loading draft: {str(e)}")
            return False, {}, f"Error loading draft: {str(e)}"
    
    def list_drafts(self) -> Tuple[bool, list, str]:
        """
        List all saved drafts.
        
        Returns:
            tuple: (success, list_of_draft_info, error_message)
        """
        try:
            if not os.path.exists(self.drafts_folder):
                return True, [], ""
            
            drafts = []
            for filename in os.listdir(self.drafts_folder):
                if filename.endswith('.json'):
                    try:
                        success, draft_data, _ = self.load_draft(filename)
                        if success:
                            draft_info = {
                                'filename': filename,
                                'subject': draft_data.get('subject', 'No Subject'),
                                'to': draft_data.get('to', 'Unknown'),
                                'created_at': draft_data.get('created_at', 'Unknown'),
                            }
                            drafts.append(draft_info)
                    except Exception as e:
                        logger.warning(f"Error reading draft {filename}: {str(e)}")
                        continue
            
            # Sort by creation time (newest first)
            drafts.sort(key=lambda x: x['created_at'], reverse=True)
            
            return True, drafts, ""
            
        except Exception as e:
            logger.error(f"Error listing drafts: {str(e)}")
            return False, [], f"Error listing drafts: {str(e)}"
    
    def delete_draft(self, filename: str) -> Tuple[bool, str]:
        """
        Delete a draft file.
        
        Args:
            filename: Draft filename to delete
            
        Returns:
            tuple: (success, error_message)
        """
        try:
            filepath = os.path.join(self.drafts_folder, filename)
            
            if not os.path.exists(filepath):
                return False, f"Draft file not found: {filename}"
            
            os.remove(filepath)
            logger.info(f"Draft deleted: {filename}")
            return True, ""
            
        except Exception as e:
            logger.error(f"Error deleting draft: {str(e)}")
            return False, f"Error deleting draft: {str(e)}"
    
    def _make_safe_filename(self, text: str, max_length: int = 50) -> str:
        """
        Create a safe filename from text.
        
        Args:
            text: Text to convert to filename
            max_length: Maximum filename length
            
        Returns:
            Safe filename string
        """
        # Remove or replace unsafe characters
        safe_chars = []
        for char in text:
            if char.isalnum() or char in '-_. ':
                safe_chars.append(char)
            else:
                safe_chars.append('_')
        
        safe_text = ''.join(safe_chars)
        
        # Replace spaces with underscores
        safe_text = safe_text.replace(' ', '_')
        
        # Remove multiple underscores
        while '__' in safe_text:
            safe_text = safe_text.replace('__', '_')
        
        # Trim to max length
        if len(safe_text) > max_length:
            safe_text = safe_text[:max_length]
        
        # Remove trailing underscores or dots
        safe_text = safe_text.rstrip('_.')
        
        return safe_text or 'untitled'
    
    def validate_email_format(self, email: str) -> bool:
        """
        Validate email format.
        
        Args:
            email: Email address to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

# Global email operations instance
email_ops = EmailOperations()
