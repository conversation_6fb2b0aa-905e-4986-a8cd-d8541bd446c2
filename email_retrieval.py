"""
Email conversation retrieval module for EMDrafter.
Handles searching and retrieving email conversations with specific senders.
"""

import imaplib
import email
import logging
import re
import html2text
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from email.header import decode_header
from gmail_auth import gmail_auth
from config import config

logger = logging.getLogger(__name__)

class EmailMessage:
    """Represents a single email message."""
    
    def __init__(self, sender: str, recipient: str, subject: str, 
                 body: str, date: datetime, message_id: str):
        self.sender = sender
        self.recipient = recipient
        self.subject = subject
        self.body = body
        self.date = date
        self.message_id = message_id
    
    def __str__(self) -> str:
        return f"From: {self.sender}\nTo: {self.recipient}\nDate: {self.date}\nSubject: {self.subject}\n\n{self.body}"

class ConversationRetriever:
    """Handles email conversation retrieval and parsing."""
    
    def __init__(self):
        """Initialize the conversation retriever."""
        self.html_converter = html2text.HTML2Text()
        self.html_converter.ignore_links = True
        self.html_converter.ignore_images = True
    
    def search_conversations(self, user_email: str, password: str, 
                           target_email: str, limit: int = 10) -> Tuple[bool, List[EmailMessage], str]:
        """
        Search for email conversations with a specific sender.
        
        Args:
            user_email: User's Gmail address
            password: User's Gmail password or App Password
            target_email: Email address to search conversations with
            limit: Maximum number of emails to retrieve
            
        Returns:
            tuple: (success, list_of_messages, error_message)
        """
        try:
            # Get IMAP connection
            imap = gmail_auth.get_imap_connection(user_email, password)
            if not imap:
                return False, [], "Failed to connect to Gmail"
            
            # Select inbox
            imap.select('INBOX')
            
            # Search for emails from or to the target email
            search_criteria = f'(OR FROM "{target_email}" TO "{target_email}")'
            status, message_ids = imap.search(None, search_criteria)
            
            if status != 'OK':
                imap.close()
                imap.logout()
                return False, [], "Failed to search emails"
            
            # Get message IDs
            message_id_list = message_ids[0].split()
            
            if not message_id_list:
                imap.close()
                imap.logout()
                return True, [], "No conversations found with this email address"
            
            # Limit the number of messages
            message_id_list = message_id_list[-limit:]  # Get most recent messages
            
            # Retrieve messages
            messages = []
            for msg_id in reversed(message_id_list):  # Most recent first
                try:
                    message = self._fetch_message(imap, msg_id)
                    if message:
                        messages.append(message)
                except Exception as e:
                    logger.warning(f"Failed to fetch message {msg_id}: {str(e)}")
                    continue
            
            # Close connection
            imap.close()
            imap.logout()
            
            # Sort messages by date (oldest first for conversation flow)
            messages.sort(key=lambda x: x.date)
            
            logger.info(f"Retrieved {len(messages)} messages from conversation with {target_email}")
            return True, messages, ""
            
        except Exception as e:
            logger.error(f"Error searching conversations: {str(e)}")
            return False, [], f"Error searching conversations: {str(e)}"
    
    def _fetch_message(self, imap: imaplib.IMAP4_SSL, msg_id: bytes) -> Optional[EmailMessage]:
        """
        Fetch and parse a single email message.
        
        Args:
            imap: IMAP connection
            msg_id: Message ID to fetch
            
        Returns:
            EmailMessage object or None if failed
        """
        try:
            # Fetch the message
            status, msg_data = imap.fetch(msg_id, '(RFC822)')
            if status != 'OK':
                return None
            
            # Parse the email
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)
            
            # Extract headers
            sender = self._decode_header(email_message.get('From', ''))
            recipient = self._decode_header(email_message.get('To', ''))
            subject = self._decode_header(email_message.get('Subject', ''))
            date_str = email_message.get('Date', '')
            message_id = email_message.get('Message-ID', '')
            
            # Parse date
            try:
                date = email.utils.parsedate_to_datetime(date_str)
            except:
                date = datetime.now()
            
            # Extract body
            body = self._extract_body(email_message)
            
            return EmailMessage(sender, recipient, subject, body, date, message_id)
            
        except Exception as e:
            logger.error(f"Error fetching message: {str(e)}")
            return None
    
    def _decode_header(self, header: str) -> str:
        """
        Decode email header.
        
        Args:
            header: Raw header string
            
        Returns:
            Decoded header string
        """
        try:
            decoded_parts = decode_header(header)
            decoded_header = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_header += part.decode(encoding)
                    else:
                        decoded_header += part.decode('utf-8', errors='ignore')
                else:
                    decoded_header += part
            
            return decoded_header.strip()
            
        except Exception as e:
            logger.warning(f"Error decoding header: {str(e)}")
            return header
    
    def _extract_body(self, email_message: email.message.Message) -> str:
        """
        Extract body text from email message.
        
        Args:
            email_message: Email message object
            
        Returns:
            Plain text body
        """
        try:
            body = ""
            
            if email_message.is_multipart():
                # Handle multipart messages
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    
                    # Skip attachments
                    if "attachment" in content_disposition:
                        continue
                    
                    if content_type == "text/plain":
                        charset = part.get_content_charset() or 'utf-8'
                        body = part.get_payload(decode=True).decode(charset, errors='ignore')
                        break
                    elif content_type == "text/html" and not body:
                        charset = part.get_content_charset() or 'utf-8'
                        html_body = part.get_payload(decode=True).decode(charset, errors='ignore')
                        body = self.html_converter.handle(html_body)
            else:
                # Handle single part messages
                content_type = email_message.get_content_type()
                charset = email_message.get_content_charset() or 'utf-8'
                
                if content_type == "text/plain":
                    body = email_message.get_payload(decode=True).decode(charset, errors='ignore')
                elif content_type == "text/html":
                    html_body = email_message.get_payload(decode=True).decode(charset, errors='ignore')
                    body = self.html_converter.handle(html_body)
            
            # Clean up the body text
            body = self._clean_body_text(body)
            
            return body
            
        except Exception as e:
            logger.error(f"Error extracting body: {str(e)}")
            return "Error extracting message body"
    
    def _clean_body_text(self, body: str) -> str:
        """
        Clean up body text by removing excessive whitespace and formatting.
        
        Args:
            body: Raw body text
            
        Returns:
            Cleaned body text
        """
        if not body:
            return ""
        
        # Remove excessive whitespace
        body = re.sub(r'\n\s*\n\s*\n', '\n\n', body)
        body = re.sub(r'[ \t]+', ' ', body)
        
        # Remove common email signatures and footers
        body = re.sub(r'\n--\s*\n.*$', '', body, flags=re.DOTALL)
        
        # Trim whitespace
        body = body.strip()
        
        return body
    
    def format_conversation(self, messages: List[EmailMessage], user_email: str) -> str:
        """
        Format conversation messages into a clean text block.
        
        Args:
            messages: List of email messages
            user_email: User's email address to identify their messages
            
        Returns:
            Formatted conversation text
        """
        if not messages:
            return "No messages found."
        
        formatted_text = "=== EMAIL CONVERSATION ===\n\n"
        
        for i, message in enumerate(messages, 1):
            # Determine if this is from the user or the other person
            is_from_user = user_email.lower() in message.sender.lower()
            speaker = "You" if is_from_user else "Them"
            
            formatted_text += f"Message {i} - {speaker} ({message.date.strftime('%Y-%m-%d %H:%M')}):\n"
            formatted_text += f"Subject: {message.subject}\n"
            formatted_text += f"{'-' * 50}\n"
            formatted_text += f"{message.body}\n"
            formatted_text += f"{'=' * 50}\n\n"
        
        return formatted_text

# Global conversation retriever instance
conversation_retriever = ConversationRetriever()
