2025-06-26 22:38:05,064 - email_operations - INFO - Created drafts folder: drafts
2025-06-26 22:38:06,046 - email_operations - INFO - Draft saved: draft_20250626_223806_Test_Subject.json
2025-06-26 22:38:06,071 - email_operations - INFO - Draft loaded: draft_20250626_223806_Test_Subject.json
2025-06-26 22:38:06,074 - error_handler - ERROR - Error #1 in test_context while testing: Test error
Traceback (most recent call last):
  File "L:\AIApps\VSCodeAPPs\EMDrafter\test_emdrafter.py", line 313, in test_error_logging
    raise ValueError("Test error")
ValueError: Test error
2025-06-26 22:38:06,074 - error_handler - ERROR - Error #1: Test error
Traceback (most recent call last):
  File "L:\AIApps\VSCodeAPPs\EMDrafter\test_emdrafter.py", line 328, in test_error_summary
    raise ValueError("Test error")
ValueError: Test error
2025-06-26 22:38:48,271 - email_operations - INFO - Draft saved: draft_20250626_223848_Test_Subject.json
2025-06-26 22:38:48,289 - email_operations - INFO - Draft loaded: draft_20250626_223848_Test_Subject.json
2025-06-26 22:38:48,301 - error_handler - ERROR - Error #1 in test_context while testing: Test error
Traceback (most recent call last):
  File "L:\AIApps\VSCodeAPPs\EMDrafter\test_emdrafter.py", line 312, in test_error_logging
    raise ValueError("Test error")
ValueError: Test error
2025-06-26 22:38:48,303 - error_handler - ERROR - Error #1: Test error
Traceback (most recent call last):
  File "L:\AIApps\VSCodeAPPs\EMDrafter\test_emdrafter.py", line 327, in test_error_summary
    raise ValueError("Test error")
ValueError: Test error
2025-06-26 22:42:56,549 - __main__ - INFO - ==================================================
2025-06-26 22:42:56,549 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 22:42:56,549 - __main__ - INFO - ==================================================
2025-06-26 22:42:56,924 - __main__ - INFO - All dependencies are available
2025-06-26 22:42:56,924 - __main__ - INFO - Configuration is valid
2025-06-26 22:42:56,924 - __main__ - INFO - Starting GUI application...
2025-06-26 22:42:59,362 - main_gui - INFO - Status: Ready
2025-06-26 22:42:59,362 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 22:45:05,447 - main_gui - INFO - Status: Testing authentication...
2025-06-26 22:45:11,635 - gmail_auth - ERROR - IMAP authentication error: b'[ALERT] Application-specific password required: https://support.google.com/accounts/answer/185833 (Failure)'
2025-06-26 22:45:11,650 - main_gui - INFO - Status: Authentication failed
2025-06-26 22:48:14,288 - main_gui - INFO - Status: Testing authentication...
2025-06-26 22:48:18,571 - gmail_auth - ERROR - SMTP connection error: 'SMTP' object has no attribute 'settimeout'
2025-06-26 22:48:18,571 - main_gui - INFO - Status: Authentication failed
2025-06-26 22:51:43,228 - gmail_auth - INFO - Successfully logged out
2025-06-26 22:51:43,255 - main_gui - INFO - EMDrafter GUI application closed
2025-06-26 22:51:43,256 - __main__ - INFO - Application closed normally
2025-06-26 22:52:32,760 - __main__ - INFO - ==================================================
2025-06-26 22:52:32,760 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 22:52:32,760 - __main__ - INFO - ==================================================
2025-06-26 22:52:34,539 - __main__ - INFO - All dependencies are available
2025-06-26 22:52:34,539 - __main__ - INFO - Configuration is valid
2025-06-26 22:52:34,539 - __main__ - INFO - Starting GUI application...
2025-06-26 22:52:35,708 - main_gui - INFO - Status: Ready
2025-06-26 22:52:35,729 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 22:54:14,556 - __main__ - INFO - ==================================================
2025-06-26 22:54:14,556 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 22:54:14,556 - __main__ - INFO - ==================================================
2025-06-26 22:54:14,791 - __main__ - INFO - All dependencies are available
2025-06-26 22:54:14,791 - __main__ - INFO - Configuration is valid
2025-06-26 22:54:14,791 - __main__ - INFO - Starting GUI application...
2025-06-26 22:54:15,119 - main_gui - INFO - Status: Ready
2025-06-26 22:54:15,119 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 22:54:39,225 - main_gui - INFO - Status: Testing authentication...
2025-06-26 22:54:43,163 - gmail_auth - INFO - Successfully <NAME_EMAIL>
2025-06-26 22:54:43,163 - main_gui - INFO - Status: Authentication successful
2025-06-26 22:54:56,436 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 22:54:59,436 - email_retrieval - INFO - Retrieved 5 messages from <NAME_EMAIL>
2025-06-26 22:54:59,608 - main_gui - INFO - Status: Found 5 messages in conversation
2025-06-26 22:56:13,728 - main_gui - INFO - Status: Generating AI reply...
2025-06-26 22:56:13,728 - deepseek_client - INFO - Making API request to Deepseek
2025-06-26 22:56:28,697 - deepseek_client - INFO - Successfully generated AI reply
2025-06-26 22:56:28,713 - main_gui - INFO - Status: AI reply generated successfully
2025-06-26 22:56:48,479 - email_operations - INFO - Draft saved: draft_20250626_225648_Re_Information_needed.json
2025-06-26 22:56:48,479 - main_gui - INFO - Status: Draft saved as draft_20250626_225648_Re_Information_needed.json
2025-06-26 23:00:21,937 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 23:00:25,390 - email_retrieval - INFO - Retrieved 2 messages from <NAME_EMAIL>
2025-06-26 23:00:25,796 - main_gui - INFO - Status: Found 2 messages in conversation
2025-06-26 23:01:00,548 - main_gui - INFO - Status: Generating AI reply...
2025-06-26 23:01:00,564 - deepseek_client - INFO - Making API request to Deepseek
2025-06-26 23:01:11,861 - deepseek_client - INFO - Successfully generated AI reply
2025-06-26 23:01:11,892 - main_gui - INFO - Status: AI reply generated successfully
2025-06-26 23:02:15,573 - gmail_auth - INFO - Successfully logged out
2025-06-26 23:02:15,573 - main_gui - INFO - EMDrafter GUI application closed
2025-06-26 23:02:15,573 - __main__ - INFO - Application closed normally
2025-06-26 23:15:01,777 - __main__ - INFO - ==================================================
2025-06-26 23:15:01,777 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 23:15:01,777 - __main__ - INFO - ==================================================
2025-06-26 23:15:02,027 - __main__ - INFO - All dependencies are available
2025-06-26 23:15:02,027 - __main__ - INFO - Configuration is valid
2025-06-26 23:15:02,027 - __main__ - INFO - Starting GUI application...
2025-06-26 23:15:02,277 - main_gui - INFO - Status: Ready
2025-06-26 23:15:02,277 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 23:15:14,693 - gmail_auth - INFO - Successfully logged out
2025-06-26 23:15:14,693 - main_gui - INFO - EMDrafter GUI application closed
2025-06-26 23:15:14,693 - __main__ - INFO - Application closed normally
2025-06-26 23:25:19,222 - __main__ - INFO - ==================================================
2025-06-26 23:25:19,222 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 23:25:19,222 - __main__ - INFO - ==================================================
2025-06-26 23:25:19,503 - __main__ - INFO - All dependencies are available
2025-06-26 23:25:19,503 - __main__ - INFO - Configuration is valid
2025-06-26 23:25:19,503 - __main__ - INFO - Starting GUI application...
2025-06-26 23:25:19,706 - main_gui - INFO - Status: Ready
2025-06-26 23:25:19,706 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 23:25:54,088 - main_gui - INFO - Status: Testing authentication...
2025-06-26 23:26:00,541 - gmail_auth - INFO - Successfully <NAME_EMAIL>
2025-06-26 23:26:00,541 - main_gui - INFO - Status: Authentication successful
2025-06-26 23:26:13,010 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 23:26:20,869 - email_retrieval - INFO - Retrieved 5 messages from <NAME_EMAIL>
2025-06-26 23:26:20,885 - main_gui - INFO - Status: Found 5 messages in conversation
2025-06-26 23:26:49,791 - main_gui - INFO - Status: Generating AI reply...
2025-06-26 23:26:49,791 - deepseek_client - INFO - Making API request to Deepseek
2025-06-26 23:27:05,479 - deepseek_client - INFO - Successfully generated AI reply
2025-06-26 23:27:05,495 - main_gui - INFO - Status: AI reply generated successfully
2025-06-26 23:27:21,331 - main_gui - INFO - Status: Saving draft locally and to Gmail...
2025-06-26 23:27:21,331 - email_operations - INFO - Draft saved locally: draft_20250626_232721_Re_Information_needed.json
2025-06-26 23:27:34,837 - email_operations - INFO - Draft successfully saved to Gmail folder: [Gmail]/Drafts
2025-06-26 23:27:35,540 - email_operations - INFO - Draft saved to Gmail Drafts folder
2025-06-26 23:27:35,555 - main_gui - INFO - Status: Draft saved: draft_20250626_232721_Re_Information_needed.json
2025-06-26 23:28:57,922 - main_gui - INFO - Status: Sending reply...
2025-06-26 23:29:01,556 - email_operations - INFO - Email sent <NAME_EMAIL>
2025-06-26 23:29:01,556 - main_gui - INFO - Status: Reply sent successfully
2025-06-26 23:31:04,739 - gmail_auth - INFO - Successfully logged out
2025-06-26 23:31:04,739 - main_gui - INFO - EMDrafter GUI application closed
2025-06-26 23:31:04,739 - __main__ - INFO - Application closed normally
2025-06-26 23:31:53,383 - __main__ - INFO - ==================================================
2025-06-26 23:31:53,383 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 23:31:53,383 - __main__ - INFO - ==================================================
2025-06-26 23:31:53,649 - __main__ - INFO - All dependencies are available
2025-06-26 23:31:53,649 - __main__ - INFO - Configuration is valid
2025-06-26 23:31:53,649 - __main__ - INFO - Starting GUI application...
2025-06-26 23:31:53,930 - main_gui - INFO - Status: Ready
2025-06-26 23:31:53,946 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 23:32:17,686 - main_gui - INFO - Status: Testing authentication...
2025-06-26 23:32:21,717 - gmail_auth - INFO - Successfully <NAME_EMAIL>
2025-06-26 23:32:21,722 - main_gui - INFO - Status: Authentication successful
2025-06-26 23:32:56,155 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 23:32:58,342 - email_retrieval - INFO - Retrieved 1 messages from <NAME_EMAIL>
2025-06-26 23:32:59,108 - main_gui - INFO - Status: Found 1 messages in conversation
2025-06-26 23:34:08,400 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 23:34:18,228 - email_retrieval - INFO - Retrieved 10 messages from <NAME_EMAIL>
2025-06-26 23:34:18,291 - main_gui - INFO - Status: Found 10 messages in conversation
2025-06-26 23:34:54,303 - gmail_auth - INFO - Successfully logged out
2025-06-26 23:34:54,303 - main_gui - INFO - EMDrafter GUI application closed
2025-06-26 23:34:54,319 - __main__ - INFO - Application closed normally
