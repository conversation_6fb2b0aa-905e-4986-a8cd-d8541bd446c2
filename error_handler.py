"""
Error handling and logging utilities for EMDrafter.
Provides comprehensive error handling, logging, and user feedback mechanisms.
"""

import logging
import traceback
import functools
from typing import Callable, Any, Optional
from datetime import datetime
import tkinter as tk
from tkinter import messagebox

logger = logging.getLogger(__name__)

class ErrorHandler:
    """Centralized error handling for the application."""
    
    def __init__(self):
        """Initialize the error handler."""
        self.error_count = 0
        self.last_error_time: Optional[datetime] = None
    
    def log_error(self, error: Exception, context: str = "", user_action: str = "") -> None:
        """
        Log an error with context information.
        
        Args:
            error: The exception that occurred
            context: Context where the error occurred
            user_action: What the user was trying to do
        """
        self.error_count += 1
        self.last_error_time = datetime.now()
        
        error_msg = f"Error #{self.error_count}"
        if context:
            error_msg += f" in {context}"
        if user_action:
            error_msg += f" while {user_action}"
        
        error_msg += f": {str(error)}"
        
        logger.error(error_msg, exc_info=True)
    
    def handle_gui_error(self, error: Exception, title: str = "Error", 
                        context: str = "", user_action: str = "") -> None:
        """
        Handle GUI errors by logging and showing user-friendly message.
        
        Args:
            error: The exception that occurred
            title: Title for the error dialog
            context: Context where the error occurred
            user_action: What the user was trying to do
        """
        self.log_error(error, context, user_action)
        
        # Create user-friendly error message
        user_msg = self._create_user_friendly_message(error, context, user_action)
        
        # Show error dialog
        messagebox.showerror(title, user_msg)
    
    def _create_user_friendly_message(self, error: Exception, context: str, user_action: str) -> str:
        """
        Create a user-friendly error message.
        
        Args:
            error: The exception that occurred
            context: Context where the error occurred
            user_action: What the user was trying to do
            
        Returns:
            User-friendly error message
        """
        error_type = type(error).__name__
        error_str = str(error)
        
        # Common error patterns and user-friendly messages
        if "authentication" in error_str.lower() or "login" in error_str.lower():
            return ("Authentication failed. Please check your email address and password.\n\n"
                   "If you're using Gmail, you may need to:\n"
                   "1. Enable 2-factor authentication\n"
                   "2. Generate an App Password\n"
                   "3. Use the App Password instead of your regular password")
        
        elif "network" in error_str.lower() or "connection" in error_str.lower():
            return ("Network connection error. Please check your internet connection and try again.\n\n"
                   "If the problem persists, it may be a temporary server issue.")
        
        elif "api" in error_str.lower() and "key" in error_str.lower():
            return ("API key error. Please check your Deepseek API key in the .env file.\n\n"
                   "Make sure you have:\n"
                   "1. A valid API key from https://platform.deepseek.com/\n"
                   "2. Sufficient API credits\n"
                   "3. Correct API key in your .env file")
        
        elif "timeout" in error_str.lower():
            return ("Operation timed out. This may be due to slow network or server response.\n\n"
                   "Please try again. If the problem persists, check your internet connection.")
        
        elif "permission" in error_str.lower() or "access" in error_str.lower():
            return ("Permission denied. Please check that the application has the necessary permissions.\n\n"
                   "You may need to run the application as administrator or check file permissions.")
        
        else:
            # Generic error message
            base_msg = f"An error occurred"
            if user_action:
                base_msg += f" while {user_action}"
            if context:
                base_msg += f" in {context}"
            
            base_msg += f".\n\nError details: {error_str}"
            
            if self.error_count > 1:
                base_msg += f"\n\nThis is error #{self.error_count}. Check the log file for more details."
            
            return base_msg
    
    def get_error_summary(self) -> str:
        """
        Get a summary of errors that have occurred.
        
        Returns:
            Error summary string
        """
        if self.error_count == 0:
            return "No errors recorded"
        
        summary = f"Total errors: {self.error_count}"
        if self.last_error_time:
            summary += f"\nLast error: {self.last_error_time.strftime('%Y-%m-%d %H:%M:%S')}"
        
        return summary

def handle_exceptions(context: str = "", user_action: str = "", 
                     show_dialog: bool = True, reraise: bool = False):
    """
    Decorator for handling exceptions in functions.
    
    Args:
        context: Context where the function is called
        user_action: What the user was trying to do
        show_dialog: Whether to show error dialog to user
        reraise: Whether to reraise the exception after handling
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_context = context or func.__name__
                
                if show_dialog:
                    error_handler.handle_gui_error(e, "Error", error_context, user_action)
                else:
                    error_handler.log_error(e, error_context, user_action)
                
                if reraise:
                    raise
                
                return None
        return wrapper
    return decorator

def safe_execute(func: Callable, *args, default_return=None, 
                context: str = "", user_action: str = "", **kwargs) -> Any:
    """
    Safely execute a function with error handling.
    
    Args:
        func: Function to execute
        *args: Arguments for the function
        default_return: Default return value if function fails
        context: Context where the function is called
        user_action: What the user was trying to do
        **kwargs: Keyword arguments for the function
        
    Returns:
        Function result or default_return if function fails
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_context = context or func.__name__
        error_handler.log_error(e, error_context, user_action)
        return default_return

class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass

class ConfigurationError(Exception):
    """Custom exception for configuration errors."""
    pass

class AuthenticationError(Exception):
    """Custom exception for authentication errors."""
    pass

class APIError(Exception):
    """Custom exception for API errors."""
    pass

def validate_email(email: str) -> None:
    """
    Validate email format and raise ValidationError if invalid.
    
    Args:
        email: Email address to validate
        
    Raises:
        ValidationError: If email format is invalid
    """
    import re
    
    if not email or not email.strip():
        raise ValidationError("Email address is required")
    
    email = email.strip()
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    if not re.match(pattern, email):
        raise ValidationError("Invalid email address format")

def validate_non_empty(value: str, field_name: str) -> None:
    """
    Validate that a string value is not empty.
    
    Args:
        value: Value to validate
        field_name: Name of the field for error message
        
    Raises:
        ValidationError: If value is empty
    """
    if not value or not value.strip():
        raise ValidationError(f"{field_name} is required")

# Global error handler instance
error_handler = ErrorHandler()
