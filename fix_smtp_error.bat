@echo off
REM Fix for SMTP settimeout error in EMDrafter
REM This script removes the problematic secure-smtplib package and reinstalls dependencies

title EMDrafter - SMTP Error Fix

echo ========================================
echo EMDrafter SMTP Error Fix
echo ========================================
echo.
echo This script will fix the "SMTP object has no attribute 'settimeout'" error
echo by removing the problematic secure-smtplib package and reinstalling dependencies.
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    pause
    exit /b 1
)

echo Current Python version:
python --version
echo.

echo [1/3] Removing problematic secure-smtplib package...
pip uninstall secure-smtplib -y
if errorlevel 1 (
    echo Note: secure-smtplib was not installed or already removed
) else (
    echo secure-smtplib removed successfully
)
echo.

echo [2/3] Reinstalling dependencies from requirements.txt...
if exist "requirements.txt" (
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to reinstall dependencies
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
    echo Dependencies reinstalled successfully
) else (
    echo WARNING: requirements.txt not found
    echo Installing basic dependencies manually...
    pip install tkinter-tooltip imaplib2 requests python-dotenv email-validator html2text cryptography python-dateutil
)
echo.

echo [3/3] Testing the fix...
python -c "
try:
    from gmail_auth import gmail_auth
    print('SUCCESS: Gmail authentication module loaded without errors')
except Exception as e:
    print(f'ERROR: {e}')
    exit(1)
"
if errorlevel 1 (
    echo The fix did not resolve the issue. Please check the error above.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Fix Applied Successfully!
echo ========================================
echo.
echo The SMTP timeout error has been fixed.
echo You can now run EMDrafter normally using run_emdrafter.bat
echo.
echo Press any key to exit...
pause >nul
