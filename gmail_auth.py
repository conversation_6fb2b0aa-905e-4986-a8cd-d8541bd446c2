"""
Gmail authentication module for EMDrafter.
Handles secure IMAP/SMTP authentication with SSL/TLS support.
"""

import imaplib
import smtplib
import ssl
import logging
from typing import Optional, Tuple
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from config import config

logger = logging.getLogger(__name__)

class GmailAuthenticator:
    """Handles Gmail authentication and connection management."""
    
    def __init__(self):
        """Initialize the Gmail authenticator."""
        self.imap_connection: Optional[imaplib.IMAP4_SSL] = None
        self.smtp_connection: Optional[smtplib.SMTP] = None
        self.email_address: Optional[str] = None
        self.is_authenticated: bool = False
    
    def authenticate(self, email: str, password: str) -> Tuple[bool, str]:
        """
        Authenticate with Gmail using IMAP and SMTP.
        
        Args:
            email: Gmail email address
            password: Gmail password or App Password
            
        Returns:
            tuple: (success, error_message)
        """
        try:
            # Validate email format
            if not self._validate_email(email):
                return False, "Invalid email format"
            
            # Test IMAP connection
            imap_success, imap_error = self._test_imap_connection(email, password)
            if not imap_success:
                return False, f"IMAP authentication failed: {imap_error}"
            
            # Test SMTP connection
            smtp_success, smtp_error = self._test_smtp_connection(email, password)
            if not smtp_success:
                return False, f"SMTP authentication failed: {smtp_error}"
            
            # Store credentials securely (in memory only)
            self.email_address = email
            self.is_authenticated = True
            
            logger.info(f"Successfully authenticated for {email}")
            return True, "Authentication successful"
            
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return False, f"Authentication error: {str(e)}"
    
    def _validate_email(self, email: str) -> bool:
        """
        Validate email format.
        
        Args:
            email: Email address to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def _test_imap_connection(self, email: str, password: str) -> Tuple[bool, str]:
        """
        Test IMAP connection to Gmail.
        
        Args:
            email: Gmail email address
            password: Gmail password or App Password
            
        Returns:
            tuple: (success, error_message)
        """
        try:
            # Create SSL context
            context = ssl.create_default_context()
            
            # Connect to Gmail IMAP server
            imap = imaplib.IMAP4_SSL(
                config.default_imap_server, 
                config.default_imap_port,
                ssl_context=context
            )
            
            # Set timeout
            imap.sock.settimeout(config.email_timeout)
            
            # Authenticate
            imap.login(email, password)
            
            # Test by selecting inbox
            imap.select('INBOX')
            
            # Close connection
            imap.close()
            imap.logout()
            
            return True, ""
            
        except imaplib.IMAP4.error as e:
            logger.error(f"IMAP authentication error: {str(e)}")
            return False, str(e)
        except Exception as e:
            logger.error(f"IMAP connection error: {str(e)}")
            return False, str(e)
    
    def _test_smtp_connection(self, email: str, password: str) -> Tuple[bool, str]:
        """
        Test SMTP connection to Gmail.
        
        Args:
            email: Gmail email address
            password: Gmail password or App Password
            
        Returns:
            tuple: (success, error_message)
        """
        try:
            # Create SMTP connection with timeout
            smtp = smtplib.SMTP(config.default_smtp_server, config.default_smtp_port, timeout=config.email_timeout)

            # Enable TLS encryption
            smtp.starttls()

            # Authenticate
            smtp.login(email, password)

            # Close connection
            smtp.quit()

            return True, ""
            
        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"SMTP authentication error: {str(e)}")
            return False, "Invalid credentials or App Password required"
        except Exception as e:
            logger.error(f"SMTP connection error: {str(e)}")
            return False, str(e)
    
    def get_imap_connection(self, email: str, password: str) -> Optional[imaplib.IMAP4_SSL]:
        """
        Get authenticated IMAP connection.
        
        Args:
            email: Gmail email address
            password: Gmail password or App Password
            
        Returns:
            IMAP connection or None if failed
        """
        try:
            # Create SSL context
            context = ssl.create_default_context()
            
            # Connect to Gmail IMAP server
            imap = imaplib.IMAP4_SSL(
                config.default_imap_server, 
                config.default_imap_port,
                ssl_context=context
            )
            
            # Set timeout
            imap.sock.settimeout(config.email_timeout)
            
            # Authenticate
            imap.login(email, password)
            
            return imap
            
        except Exception as e:
            logger.error(f"Failed to create IMAP connection: {str(e)}")
            return None
    
    def get_smtp_connection(self, email: str, password: str) -> Optional[smtplib.SMTP]:
        """
        Get authenticated SMTP connection.
        
        Args:
            email: Gmail email address
            password: Gmail password or App Password
            
        Returns:
            SMTP connection or None if failed
        """
        try:
            # Create SMTP connection with timeout
            smtp = smtplib.SMTP(config.default_smtp_server, config.default_smtp_port, timeout=config.email_timeout)

            # Enable TLS encryption
            smtp.starttls()

            # Authenticate
            smtp.login(email, password)

            return smtp
            
        except Exception as e:
            logger.error(f"Failed to create SMTP connection: {str(e)}")
            return None
    
    def logout(self) -> None:
        """Clean up connections and reset authentication state."""
        try:
            if self.imap_connection:
                self.imap_connection.close()
                self.imap_connection.logout()
                self.imap_connection = None
            
            if self.smtp_connection:
                self.smtp_connection.quit()
                self.smtp_connection = None
            
            self.email_address = None
            self.is_authenticated = False
            
            logger.info("Successfully logged out")
            
        except Exception as e:
            logger.error(f"Error during logout: {str(e)}")

# Global authenticator instance
gmail_auth = GmailAuthenticator()
