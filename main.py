"""
EMDrafter - AI-Powered Email Reply Assistant
Main entry point for the Windows desktop application.

This application provides:
- Secure Gmail authentication via IMAP/SMTP
- Email conversation retrieval and parsing
- AI-powered reply generation using Deepseek API
- Email sending and draft management
- User-friendly Windows GUI

Author: EMDrafter Development Team
Version: 1.0.0
"""

import sys
import os
import logging
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def setup_logging():
    """Setup logging configuration for the application."""
    try:
        from config import config
        
        # Create logs directory if it doesn't exist
        log_file = Path(config.log_file)
        log_file.parent.mkdir(exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, config.log_level.upper(), logging.INFO),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(config.log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info("=" * 50)
        logger.info(f"Starting {config.app_name} v{config.app_version}")
        logger.info("=" * 50)
        
    except Exception as e:
        # Fallback logging configuration
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('emdrafter.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to setup logging from config: {e}")

def check_dependencies():
    """Check if all required dependencies are available."""
    logger = logging.getLogger(__name__)
    missing_deps = []
    
    # Check required modules
    required_modules = [
        'tkinter',
        'requests',
        'dotenv',
        'html2text',
        'cryptography',
        'dateutil'
    ]
    
    for module in required_modules:
        try:
            if module == 'tkinter':
                import tkinter
            elif module == 'requests':
                import requests
            elif module == 'dotenv':
                from dotenv import load_dotenv
            elif module == 'html2text':
                import html2text
            elif module == 'cryptography':
                import cryptography
            elif module == 'dateutil':
                from dateutil import parser
        except ImportError as e:
            missing_deps.append(f"{module}: {str(e)}")
    
    if missing_deps:
        logger.error("Missing dependencies:")
        for dep in missing_deps:
            logger.error(f"  - {dep}")
        
        print("\nError: Missing required dependencies!")
        print("Please install them using:")
        print("  pip install -r requirements.txt")
        print("\nMissing dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        return False
    
    logger.info("All dependencies are available")
    return True

def check_configuration():
    """Check application configuration."""
    logger = logging.getLogger(__name__)
    
    try:
        from config import config
        
        # Check if .env file exists
        env_file = Path('.env')
        if not env_file.exists():
            logger.warning(".env file not found - using default configuration")
            print("\nWarning: .env file not found!")
            print("Please copy .env.template to .env and configure your settings.")
            print("The application will use default settings, but you'll need to configure:")
            print("  - DEEPSEEK_API_KEY for AI reply generation")
            return True  # Continue anyway with defaults
        
        # Validate configuration
        is_valid, errors = config.validate_config()
        if not is_valid:
            logger.warning("Configuration validation issues:")
            for error in errors:
                logger.warning(f"  - {error}")
            
            print("\nWarning: Configuration issues detected:")
            for error in errors:
                print(f"  - {error}")
            print("Please check your .env file configuration.")
            return True  # Continue anyway - GUI will show warnings
        
        logger.info("Configuration is valid")
        return True
        
    except Exception as e:
        logger.error(f"Configuration check failed: {e}")
        print(f"\nError: Configuration check failed: {e}")
        return False

def main():
    """Main entry point for EMDrafter application."""
    try:
        # Setup logging first
        setup_logging()
        logger = logging.getLogger(__name__)
        
        # Check dependencies
        if not check_dependencies():
            sys.exit(1)
        
        # Check configuration
        if not check_configuration():
            sys.exit(1)
        
        # Import and run the GUI application
        logger.info("Starting GUI application...")
        from main_gui import EMDrafterGUI
        
        app = EMDrafterGUI()
        app.run()
        
        logger.info("Application closed normally")
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        print("\nApplication interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}", exc_info=True)
        print(f"\nFatal error: {str(e)}")
        print("Check the log file for more details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
