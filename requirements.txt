# Core GUI Framework
tkinter-tooltip==2.2.0

# Email handling (SMTP is built-in)
imaplib2==3.6

# HTTP requests for API calls
requests==2.31.0

# Environment variable management
python-dotenv==1.0.0

# Email parsing and handling
email-validator==2.1.0
html2text==2020.1.16

# Security and encryption
cryptography==41.0.7

# Date/time handling
python-dateutil==2.8.2

# JSON handling (built-in, but explicit for clarity)
# json - built-in

# Threading support (built-in)
# threading - built-in

# Logging (built-in)
# logging - built-in

# Regular expressions (built-in)
# re - built-in

# Base64 encoding (built-in)
# base64 - built-in

# SSL/TLS support (built-in)
# ssl - built-in

# IMAP client (built-in)
# imaplib - built-in

# SMTP client (built-in)
# smtplib - built-in

# Email message handling (built-in)
# email - built-in

# GUI framework (built-in with Python)
# tkinter - built-in

# Password input (built-in)
# getpass - built-in

# Configuration parser (built-in)
# configparser - built-in

# Operating system interface (built-in)
# os - built-in

# System-specific parameters (built-in)
# sys - built-in

# Time-related functions (built-in)
# time - built-in
