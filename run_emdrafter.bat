@echo off
REM EMDrafter Launcher Script for Windows
REM This script provides an easy way to launch EMDrafter with proper error handling

title EMDrafter - AI Email Reply Assistant

echo ========================================
echo EMDrafter - AI Email Reply Assistant
echo Version 1.0.0
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or later from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found: 
python --version

REM Check if we're in the correct directory
if not exist "main.py" (
    echo ERROR: main.py not found in current directory
    echo Please make sure you're running this script from the EMDrafter folder
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo WARNING: .env file not found
    echo Please copy .env.template to .env and configure your settings
    echo.
    if exist ".env.template" (
        echo Found .env.template file. Would you like to copy it to .env now?
        set /p choice="Type 'y' to copy, or any other key to continue: "
        if /i "!choice!"=="y" (
            copy ".env.template" ".env"
            echo .env file created. Please edit it with your API key before continuing.
            echo Opening .env file for editing...
            notepad .env
            echo Please save and close the file, then press any key to continue...
            pause
        )
    )
)

echo.
echo Checking dependencies...

REM Check if required packages are installed
python -c "import tkinter, requests, dotenv, html2text, cryptography" >nul 2>&1
if errorlevel 1 (
    echo Some required packages are missing.
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        echo Please run: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo Dependencies OK
echo.

REM Run the application
echo Starting EMDrafter...
echo.
python main.py

REM Check exit code
if errorlevel 1 (
    echo.
    echo Application exited with an error.
    echo Check emdrafter.log for details.
    echo.
)

echo.
echo EMDrafter has closed.
pause
