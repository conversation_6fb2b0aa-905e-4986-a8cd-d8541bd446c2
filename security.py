"""
Security utilities for EMDrafter.
Implements security measures including secure password handling, input validation,
and safe API key management.
"""

import os
import re
import logging
import hashlib
import secrets
from typing import Op<PERSON>, <PERSON><PERSON>, List
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

logger = logging.getLogger(__name__)

class SecurityManager:
    """Manages security operations for the application."""
    
    def __init__(self):
        """Initialize the security manager."""
        self._session_key: Optional[bytes] = None
        self._cipher_suite: Optional[Fernet] = None
    
    def generate_session_key(self, password: str, salt: Optional[bytes] = None) -> bytes:
        """
        Generate a session key from password for temporary encryption.
        
        Args:
            password: Password to derive key from
            salt: Optional salt (generated if not provided)
            
        Returns:
            Session key bytes
        """
        if salt is None:
            salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key
    
    def encrypt_sensitive_data(self, data: str, key: bytes) -> str:
        """
        Encrypt sensitive data using Fernet symmetric encryption.
        
        Args:
            data: Data to encrypt
            key: Encryption key
            
        Returns:
            Encrypted data as base64 string
        """
        try:
            cipher_suite = Fernet(key)
            encrypted_data = cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"Encryption error: {str(e)}")
            raise SecurityError(f"Failed to encrypt data: {str(e)}")
    
    def decrypt_sensitive_data(self, encrypted_data: str, key: bytes) -> str:
        """
        Decrypt sensitive data using Fernet symmetric encryption.
        
        Args:
            encrypted_data: Encrypted data as base64 string
            key: Decryption key
            
        Returns:
            Decrypted data
        """
        try:
            cipher_suite = Fernet(key)
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = cipher_suite.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Decryption error: {str(e)}")
            raise SecurityError(f"Failed to decrypt data: {str(e)}")
    
    def secure_compare(self, a: str, b: str) -> bool:
        """
        Perform timing-safe string comparison.
        
        Args:
            a: First string
            b: Second string
            
        Returns:
            True if strings are equal, False otherwise
        """
        return secrets.compare_digest(a.encode(), b.encode())
    
    def generate_secure_token(self, length: int = 32) -> str:
        """
        Generate a cryptographically secure random token.
        
        Args:
            length: Token length in bytes
            
        Returns:
            Secure token as hex string
        """
        return secrets.token_hex(length)
    
    def hash_data(self, data: str, salt: Optional[str] = None) -> Tuple[str, str]:
        """
        Hash data with salt using SHA-256.
        
        Args:
            data: Data to hash
            salt: Optional salt (generated if not provided)
            
        Returns:
            Tuple of (hash, salt)
        """
        if salt is None:
            salt = secrets.token_hex(16)
        
        hash_obj = hashlib.sha256()
        hash_obj.update((data + salt).encode())
        hash_value = hash_obj.hexdigest()
        
        return hash_value, salt

class InputValidator:
    """Validates and sanitizes user input."""
    
    @staticmethod
    def validate_email(email: str) -> Tuple[bool, str]:
        """
        Validate email address format.
        
        Args:
            email: Email address to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not email or not email.strip():
            return False, "Email address is required"
        
        email = email.strip()
        
        # Check length
        if len(email) > 254:
            return False, "Email address is too long"
        
        # Check format
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, email):
            return False, "Invalid email address format"
        
        # Check for dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', '\n', '\r', '\t']
        if any(char in email for char in dangerous_chars):
            return False, "Email contains invalid characters"
        
        return True, ""
    
    @staticmethod
    def validate_password(password: str) -> Tuple[bool, str]:
        """
        Validate password strength.
        
        Args:
            password: Password to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not password:
            return False, "Password is required"
        
        # Check minimum length
        if len(password) < 8:
            return False, "Password must be at least 8 characters long"
        
        # Check maximum length (prevent DoS)
        if len(password) > 128:
            return False, "Password is too long"
        
        return True, ""
    
    @staticmethod
    def sanitize_text(text: str, max_length: int = 10000) -> str:
        """
        Sanitize text input by removing dangerous characters.
        
        Args:
            text: Text to sanitize
            max_length: Maximum allowed length
            
        Returns:
            Sanitized text
        """
        if not text:
            return ""
        
        # Truncate if too long
        if len(text) > max_length:
            text = text[:max_length]
        
        # Remove null bytes and control characters (except newlines and tabs)
        sanitized = ""
        for char in text:
            if ord(char) >= 32 or char in ['\n', '\r', '\t']:
                sanitized += char
        
        return sanitized
    
    @staticmethod
    def validate_api_key(api_key: str) -> Tuple[bool, str]:
        """
        Validate API key format.
        
        Args:
            api_key: API key to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not api_key or not api_key.strip():
            return False, "API key is required"
        
        api_key = api_key.strip()
        
        # Check length (typical API keys are 32-128 characters)
        if len(api_key) < 16:
            return False, "API key is too short"
        
        if len(api_key) > 256:
            return False, "API key is too long"
        
        # Check for valid characters (alphanumeric, hyphens, underscores)
        if not re.match(r'^[a-zA-Z0-9_-]+$', api_key):
            return False, "API key contains invalid characters"
        
        return True, ""

class SecureStorage:
    """Handles secure storage of sensitive data."""
    
    def __init__(self):
        """Initialize secure storage."""
        self._memory_store = {}
    
    def store_temporarily(self, key: str, value: str, encrypt: bool = True) -> None:
        """
        Store sensitive data temporarily in memory.
        
        Args:
            key: Storage key
            value: Value to store
            encrypt: Whether to encrypt the value
        """
        if encrypt:
            # Use a simple XOR encryption for temporary storage
            encrypted_value = self._xor_encrypt(value)
            self._memory_store[key] = encrypted_value
        else:
            self._memory_store[key] = value
    
    def retrieve_temporarily(self, key: str, encrypted: bool = True) -> Optional[str]:
        """
        Retrieve temporarily stored data.
        
        Args:
            key: Storage key
            encrypted: Whether the stored value is encrypted
            
        Returns:
            Retrieved value or None if not found
        """
        if key not in self._memory_store:
            return None
        
        value = self._memory_store[key]
        
        if encrypted:
            return self._xor_decrypt(value)
        else:
            return value
    
    def clear_temporary(self, key: Optional[str] = None) -> None:
        """
        Clear temporarily stored data.
        
        Args:
            key: Specific key to clear, or None to clear all
        """
        if key:
            self._memory_store.pop(key, None)
        else:
            self._memory_store.clear()
    
    def _xor_encrypt(self, data: str) -> str:
        """Simple XOR encryption for temporary storage."""
        key = "EMDrafter_Temp_Key_2024"  # Simple key for basic obfuscation
        encrypted = ""
        for i, char in enumerate(data):
            encrypted += chr(ord(char) ^ ord(key[i % len(key)]))
        return base64.b64encode(encrypted.encode()).decode()
    
    def _xor_decrypt(self, encrypted_data: str) -> str:
        """Simple XOR decryption for temporary storage."""
        key = "EMDrafter_Temp_Key_2024"
        try:
            decoded = base64.b64decode(encrypted_data.encode()).decode()
            decrypted = ""
            for i, char in enumerate(decoded):
                decrypted += chr(ord(char) ^ ord(key[i % len(key)]))
            return decrypted
        except:
            return ""

class SecurityError(Exception):
    """Custom exception for security-related errors."""
    pass

def check_file_permissions(file_path: str) -> Tuple[bool, str]:
    """
    Check if file has appropriate permissions.
    
    Args:
        file_path: Path to file to check
        
    Returns:
        Tuple of (is_secure, warning_message)
    """
    try:
        if not os.path.exists(file_path):
            return True, ""
        
        # Check if file is readable by others (basic check)
        stat_info = os.stat(file_path)
        mode = stat_info.st_mode
        
        # Check if file is world-readable (not ideal for sensitive files)
        if mode & 0o004:
            return False, f"File {file_path} is readable by others"
        
        return True, ""
        
    except Exception as e:
        logger.warning(f"Could not check file permissions: {str(e)}")
        return True, ""  # Don't block operation if we can't check

# Global instances
security_manager = SecurityManager()
input_validator = InputValidator()
secure_storage = SecureStorage()
