@echo off
REM EMDrafter Setup Script for Windows
REM This script helps set up EMDrafter for first-time use

title EMDrafter Setup

echo ========================================
echo EMDrafter Setup
echo AI Email Reply Assistant
echo ========================================
echo.
echo This script will help you set up EMDrafter for first-time use.
echo.

REM Check if Python is installed
echo [1/5] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or later:
    echo 1. Go to https://python.org/downloads/
    echo 2. Download the latest Python version
    echo 3. During installation, check "Add Python to PATH"
    echo 4. Restart this script after installation
    echo.
    pause
    exit /b 1
)

python --version
echo Python OK
echo.

REM Install dependencies
echo [2/5] Installing dependencies...
if exist "requirements.txt" (
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install some dependencies
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
    echo Dependencies installed successfully
) else (
    echo WARNING: requirements.txt not found
    echo Installing basic dependencies...
    pip install requests python-dotenv html2text cryptography python-dateutil
)
echo.

REM Create .env file
echo [3/5] Setting up configuration...
if not exist ".env" (
    if exist ".env.template" (
        copy ".env.template" ".env"
        echo Created .env file from template
    ) else (
        echo Creating basic .env file...
        echo # EMDrafter Configuration > .env
        echo DEEPSEEK_API_KEY=your_deepseek_api_key_here >> .env
        echo DEEPSEEK_API_BASE=https://api.deepseek.com >> .env
        echo APP_NAME=EMDrafter >> .env
        echo LOG_LEVEL=INFO >> .env
        echo MAX_CONVERSATION_LENGTH=10000 >> .env
        echo EMAIL_TIMEOUT=30 >> .env
        echo API_TIMEOUT=60 >> .env
    )
    echo .env file created
) else (
    echo .env file already exists
)
echo.

REM Test basic functionality
echo [4/5] Testing basic functionality...
python -c "
try:
    from config import config
    from security import input_validator
    from error_handler import error_handler
    print('Core modules loaded successfully')
except Exception as e:
    print(f'Error loading modules: {e}')
    exit(1)
"
if errorlevel 1 (
    echo ERROR: Basic functionality test failed
    echo Please check the installation
    pause
    exit /b 1
)
echo Basic functionality test passed
echo.

REM Configuration guidance
echo [5/5] Configuration guidance...
echo.
echo Setup is almost complete! You need to configure your API key:
echo.
echo 1. Get a Deepseek API key:
echo    - Go to https://platform.deepseek.com/
echo    - Sign up for an account
echo    - Generate an API key
echo.
echo 2. Configure Gmail access:
echo    - Enable 2-factor authentication on your Google account
echo    - Generate an App Password for EMDrafter
echo    - Use the App Password (not your regular password) in the application
echo.
echo 3. Edit your .env file with the API key
echo.

set /p edit_env="Would you like to edit the .env file now? (y/n): "
if /i "%edit_env%"=="y" (
    echo Opening .env file for editing...
    if exist "notepad.exe" (
        notepad .env
    ) else (
        start .env
    )
    echo.
    echo Please:
    echo 1. Replace 'your_deepseek_api_key_here' with your actual API key
    echo 2. Save and close the file
    echo.
    pause
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo To start EMDrafter:
echo 1. Double-click 'run_emdrafter.bat', or
echo 2. Run 'python main.py' from command line
echo.
echo For help, see:
echo - USER_MANUAL.md for detailed instructions
echo - README.md for overview and troubleshooting
echo.
echo Press any key to exit setup...
pause >nul
