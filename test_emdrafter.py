"""
Test suite for EMDrafter application.
Tests core functionality including authentication, email retrieval, and AI integration.
"""

import unittest
import os
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Import modules to test
from config import Config
from security import InputValidator, SecurityManager, SecureStorage
from email_operations import EmailOperations
from error_handler import ErrorHandler, ValidationError

class TestConfig(unittest.TestCase):
    """Test configuration management."""
    
    def test_config_initialization(self):
        """Test config initialization with default values."""
        config = Config()
        self.assertEqual(config.app_name, 'EMDrafter')
        self.assertEqual(config.default_imap_server, 'imap.gmail.com')
        self.assertEqual(config.default_smtp_server, 'smtp.gmail.com')
    
    def test_config_validation(self):
        """Test configuration validation."""
        config = Config()
        # Without API key, validation should fail
        is_valid, errors = config.validate_config()
        self.assertFalse(is_valid)
        self.assertTrue(any('DEEPSEEK_API_KEY' in error for error in errors))

class TestInputValidator(unittest.TestCase):
    """Test input validation functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.validator = InputValidator()
    
    def test_valid_email(self):
        """Test validation of valid email addresses."""
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for email in valid_emails:
            is_valid, error = self.validator.validate_email(email)
            self.assertTrue(is_valid, f"Email {email} should be valid")
            self.assertEqual(error, "")
    
    def test_invalid_email(self):
        """Test validation of invalid email addresses."""
        invalid_emails = [
            '',
            'invalid',
            '@example.com',
            'test@',
            'test@example',
            'test@.com',
            'test<script>@example.com'
        ]
        
        for email in invalid_emails:
            is_valid, error = self.validator.validate_email(email)
            self.assertFalse(is_valid, f"Email {email} should be invalid")
            self.assertNotEqual(error, "")
    
    def test_password_validation(self):
        """Test password validation."""
        # Valid passwords
        valid_passwords = [
            'password123',
            'MySecureP@ssw0rd',
            'a' * 20
        ]
        
        for password in valid_passwords:
            is_valid, error = self.validator.validate_password(password)
            self.assertTrue(is_valid, f"Password should be valid")
            self.assertEqual(error, "")
        
        # Invalid passwords
        invalid_passwords = [
            '',
            'short',
            'a' * 200  # Too long
        ]
        
        for password in invalid_passwords:
            is_valid, error = self.validator.validate_password(password)
            self.assertFalse(is_valid, f"Password should be invalid")
            self.assertNotEqual(error, "")
    
    def test_text_sanitization(self):
        """Test text sanitization."""
        # Test normal text
        normal_text = "Hello, this is a normal message."
        sanitized = self.validator.sanitize_text(normal_text)
        self.assertEqual(sanitized, normal_text)
        
        # Test text with control characters
        text_with_controls = "Hello\x00\x01\x02World"
        sanitized = self.validator.sanitize_text(text_with_controls)
        self.assertEqual(sanitized, "HelloWorld")
        
        # Test text length limit
        long_text = "a" * 20000
        sanitized = self.validator.sanitize_text(long_text, max_length=100)
        self.assertEqual(len(sanitized), 100)
    
    def test_api_key_validation(self):
        """Test API key validation."""
        # Valid API keys
        valid_keys = [
            'sk-1234567890abcdef',
            'api_key_123456789',
            'a' * 32
        ]
        
        for key in valid_keys:
            is_valid, error = self.validator.validate_api_key(key)
            self.assertTrue(is_valid, f"API key should be valid")
            self.assertEqual(error, "")
        
        # Invalid API keys
        invalid_keys = [
            '',
            'short',
            'key with spaces',
            'key@with#special$chars',
            'a' * 300  # Too long
        ]
        
        for key in invalid_keys:
            is_valid, error = self.validator.validate_api_key(key)
            self.assertFalse(is_valid, f"API key should be invalid")
            self.assertNotEqual(error, "")

class TestSecurityManager(unittest.TestCase):
    """Test security management functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.security_manager = SecurityManager()
    
    def test_session_key_generation(self):
        """Test session key generation."""
        password = "test_password"
        key1 = self.security_manager.generate_session_key(password)
        key2 = self.security_manager.generate_session_key(password)
        
        # Keys should be different due to different salts
        self.assertNotEqual(key1, key2)
        self.assertEqual(len(key1), 44)  # Base64 encoded 32-byte key
    
    def test_encryption_decryption(self):
        """Test data encryption and decryption."""
        password = "test_password"
        key = self.security_manager.generate_session_key(password)
        
        original_data = "This is sensitive data"
        encrypted = self.security_manager.encrypt_sensitive_data(original_data, key)
        decrypted = self.security_manager.decrypt_sensitive_data(encrypted, key)
        
        self.assertEqual(original_data, decrypted)
        self.assertNotEqual(original_data, encrypted)
    
    def test_secure_compare(self):
        """Test timing-safe string comparison."""
        string1 = "password123"
        string2 = "password123"
        string3 = "different"
        
        self.assertTrue(self.security_manager.secure_compare(string1, string2))
        self.assertFalse(self.security_manager.secure_compare(string1, string3))
    
    def test_secure_token_generation(self):
        """Test secure token generation."""
        token1 = self.security_manager.generate_secure_token()
        token2 = self.security_manager.generate_secure_token()
        
        self.assertNotEqual(token1, token2)
        self.assertEqual(len(token1), 64)  # 32 bytes as hex = 64 chars
    
    def test_data_hashing(self):
        """Test data hashing with salt."""
        data = "test_data"
        hash1, salt1 = self.security_manager.hash_data(data)
        hash2, salt2 = self.security_manager.hash_data(data)
        
        # Different salts should produce different hashes
        self.assertNotEqual(hash1, hash2)
        self.assertNotEqual(salt1, salt2)
        
        # Same salt should produce same hash
        hash3, _ = self.security_manager.hash_data(data, salt1)
        self.assertEqual(hash1, hash3)

class TestSecureStorage(unittest.TestCase):
    """Test secure storage functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.storage = SecureStorage()
    
    def test_temporary_storage(self):
        """Test temporary storage and retrieval."""
        key = "test_key"
        value = "sensitive_data"
        
        # Store and retrieve encrypted
        self.storage.store_temporarily(key, value, encrypt=True)
        retrieved = self.storage.retrieve_temporarily(key, encrypted=True)
        self.assertEqual(value, retrieved)
        
        # Store and retrieve unencrypted
        self.storage.store_temporarily(key, value, encrypt=False)
        retrieved = self.storage.retrieve_temporarily(key, encrypted=False)
        self.assertEqual(value, retrieved)
    
    def test_storage_clearing(self):
        """Test clearing stored data."""
        self.storage.store_temporarily("key1", "value1")
        self.storage.store_temporarily("key2", "value2")
        
        # Clear specific key
        self.storage.clear_temporary("key1")
        self.assertIsNone(self.storage.retrieve_temporarily("key1"))
        self.assertIsNotNone(self.storage.retrieve_temporarily("key2"))
        
        # Clear all
        self.storage.clear_temporary()
        self.assertIsNone(self.storage.retrieve_temporarily("key2"))

class TestEmailOperations(unittest.TestCase):
    """Test email operations functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.email_ops = EmailOperations()
        # Create temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()
        self.email_ops.drafts_folder = self.temp_dir
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_draft_saving_and_loading(self):
        """Test saving and loading email drafts."""
        draft_data = {
            'from': '<EMAIL>',
            'to': '<EMAIL>',
            'subject': 'Test Subject',
            'body': 'Test email body',
            'reply_to_message_id': None
        }
        
        # Save draft
        success, filename, error = self.email_ops.save_draft(
            draft_data['from'], draft_data['to'], 
            draft_data['subject'], draft_data['body']
        )
        
        self.assertTrue(success)
        self.assertNotEqual(filename, "")
        self.assertEqual(error, "")
        
        # Load draft
        success, loaded_data, error = self.email_ops.load_draft(filename)
        
        self.assertTrue(success)
        self.assertEqual(loaded_data['from'], draft_data['from'])
        self.assertEqual(loaded_data['to'], draft_data['to'])
        self.assertEqual(loaded_data['subject'], draft_data['subject'])
        self.assertEqual(loaded_data['body'], draft_data['body'])
    
    def test_safe_filename_generation(self):
        """Test safe filename generation."""
        test_cases = [
            ("Normal Subject", "Normal_Subject"),
            ("Subject with spaces", "Subject_with_spaces"),
            ("Subject/with\\dangerous:chars", "Subject_with_dangerous_chars"),
            ("A" * 100, "A" * 50),  # Test length limit
            ("", "untitled")
        ]
        
        for input_text, expected in test_cases:
            result = self.email_ops._make_safe_filename(input_text)
            self.assertEqual(result, expected)

class TestErrorHandler(unittest.TestCase):
    """Test error handling functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.error_handler = ErrorHandler()
    
    def test_error_logging(self):
        """Test error logging functionality."""
        initial_count = self.error_handler.error_count
        
        try:
            raise ValueError("Test error")
        except ValueError as e:
            self.error_handler.log_error(e, "test_context", "testing")
        
        self.assertEqual(self.error_handler.error_count, initial_count + 1)
        self.assertIsNotNone(self.error_handler.last_error_time)
    
    def test_error_summary(self):
        """Test error summary generation."""
        # Initially no errors
        summary = self.error_handler.get_error_summary()
        self.assertIn("No errors", summary)
        
        # After logging an error
        try:
            raise ValueError("Test error")
        except ValueError as e:
            self.error_handler.log_error(e)
        
        summary = self.error_handler.get_error_summary()
        self.assertIn("Total errors: 1", summary)

def run_tests():
    """Run all tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestConfig,
        TestInputValidator,
        TestSecurityManager,
        TestSecureStorage,
        TestEmailOperations,
        TestErrorHandler
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
